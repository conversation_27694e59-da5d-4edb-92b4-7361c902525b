Stack trace:
Frame         Function      Args
0007FFFF8B70  00021006116E (00021028DEE8, 000210272B3E, 0007FFFF8B70, 0007FFFF7A70) msys-2.0.dll+0x2116E
0007FFFF8B70  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFF8B70  0002100469F2 (00021028DF99, 0007FFFF8A28, 0007FFFF8B70, 000000000000) msys-2.0.dll+0x69F2
0007FFFF8B70  00021006A3FE (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A3FE
0007FFFF8B70  00021006A525 (0007FFFF8B80, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A525
0001004F94B7  00021006B985 (0007FFFF8B80, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B985
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFCA9DA0000 ntdll.dll
7FFCA7ED0000 KERNEL32.DLL
7FFCA7360000 KERNELBASE.dll
7FFCA9B90000 USER32.dll
7FFCA7330000 win32u.dll
7FFCA7E90000 GDI32.dll
000210040000 msys-2.0.dll
7FFCA7A00000 gdi32full.dll
7FFCA6EF0000 msvcp_win.dll
7FFCA7810000 ucrtbase.dll
7FFCA8E20000 advapi32.dll
7FFCA7BE0000 msvcrt.dll
7FFCA7C90000 sechost.dll
7FFCA9A70000 RPCRT4.dll
7FFCA6520000 CRYPTBASE.DLL
7FFCA7960000 bcryptPrimitives.dll
7FFCA98E0000 IMM32.DLL
