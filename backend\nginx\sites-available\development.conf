upstream revproxy {
    least_conn;  # Use least connections load balancing
    server backend:8000;  # Use the service name from docker-compose
    keepalive 32;  # Maintain a connection pool
}

server {
    listen 80;
    server_name localhost;

    client_max_body_size 100M;

    # Special configuration for Server-Sent Events (SSE)
    location /api/projects/courses/registrations/notifications/stream/ {
        proxy_pass http://revproxy;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Remove any existing CORS headers from backend to avoid conflicts
        proxy_hide_header 'Access-Control-Allow-Origin';
        proxy_hide_header 'Access-Control-Allow-Methods';
        proxy_hide_header 'Access-Control-Allow-Headers';
        proxy_hide_header 'Access-Control-Allow-Credentials';

        # SSE specific settings - critical for proper streaming
        proxy_buffering off;
        proxy_cache off;
        proxy_set_header Connection '';
        proxy_http_version 1.1;
        chunked_transfer_encoding off;

        # Disable nginx buffering for real-time streaming
        proxy_request_buffering off;
        proxy_max_temp_file_size 0;

        # Reasonable timeouts for SSE connections
        proxy_read_timeout 3600s;  # 1 hour
        proxy_send_timeout 3600s;  # 1 hour
        proxy_connect_timeout 60s;

        # CORS headers for SSE - set only by nginx
        add_header 'Access-Control-Allow-Origin' 'http://localhost:3000' always;
        add_header 'Access-Control-Allow-Credentials' 'true' always;
        add_header 'Access-Control-Allow-Methods' 'GET, OPTIONS' always;
        add_header 'Access-Control-Allow-Headers' 'Authorization,Content-Type,Accept,Origin,User-Agent,DNT,Cache-Control,X-Mx-ReqToken,Keep-Alive,X-Requested-With,If-Modified-Since' always;
        add_header 'Cache-Control' 'no-cache' always;
    }

    location / {
        proxy_pass http://revproxy;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

        # Remove any existing CORS headers from backend
        proxy_hide_header 'Access-Control-Allow-Origin';
        proxy_hide_header 'Access-Control-Allow-Methods';
        proxy_hide_header 'Access-Control-Allow-Headers';
        proxy_hide_header 'Access-Control-Allow-Credentials';

        # Set CORS headers to allow all origins (but not for SSE endpoints)
        add_header 'Access-Control-Allow-Origin' '*' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS, PATCH' always;
        add_header 'Access-Control-Allow-Headers' 'Authorization,Content-Type,Accept,Origin,User-Agent,DNT,Cache-Control,X-Mx-ReqToken,Keep-Alive,X-Requested-With,If-Modified-Since' always;

        # Handle OPTIONS method for CORS (but not for SSE endpoints)
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' '*' always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS, PATCH' always;
            add_header 'Access-Control-Allow-Headers' 'Authorization,Content-Type,Accept,Origin,User-Agent,DNT,Cache-Control,X-Mx-ReqToken,Keep-Alive,X-Requested-With,If-Modified-Since' always;
            add_header 'Content-Type' 'text/plain charset=UTF-8' always;
            add_header 'Content-Length' 0 always;
            return 204;
        }
    }

    location /static/ {
        alias /app/staticfiles/;
        expires 30d;
        access_log off;
    }

    location /media/ {
        alias /app/media/;
        expires 30d;
        access_log off;
    }
} 