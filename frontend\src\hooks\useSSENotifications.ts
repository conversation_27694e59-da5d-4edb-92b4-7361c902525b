import { useState, useEffect, useRef, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';

interface NotificationData {
  type: 'connected' | 'update' | 'heartbeat' | 'error';
  count?: number;
  requests?: any[];
  message?: string;
  timestamp?: number;
}

interface UseSSENotificationsReturn {
  pendingCount: number;
  pendingRequests: any[];
  isConnected: boolean;
  error: string | null;
  reconnect: () => void;
}

export const useSSENotifications = (): UseSSENotificationsReturn => {
  const { user } = useAuth();
  const [pendingCount, setPendingCount] = useState(0);
  const [pendingRequests, setPendingRequests] = useState<any[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const eventSourceRef = useRef<EventSource | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttemptsRef = useRef(0);
  const maxReconnectAttempts = 5;

  const cleanup = useCallback(() => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    setIsConnected(false);
  }, []);

  const connect = useCallback(() => {
    // Only connect for authenticated educators
    if (!user || user.user_type !== 'E') {
      return;
    }

    cleanup();

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        setError('No authentication token found');
        return;
      }

      // Create SSE connection with token as query parameter
      const baseURL = import.meta.env.VITE_API_URL || 'http://localhost/api';
      const sseUrl = `${baseURL}/projects/courses/registrations/notifications/stream/?token=${token}`;

      console.log('Attempting SSE connection to:', sseUrl);

      const eventSource = new EventSource(sseUrl);

      eventSourceRef.current = eventSource;

      eventSource.onopen = () => {
        console.log('SSE connection opened successfully');
        console.log('EventSource readyState:', eventSource.readyState);
        setIsConnected(true);
        setError(null);
        reconnectAttemptsRef.current = 0;
      };

      eventSource.onmessage = (event) => {
        try {
          const data: NotificationData = JSON.parse(event.data);
          
          switch (data.type) {
            case 'connected':
              console.log('SSE connected:', data.message);
              break;
              
            case 'update':
              if (typeof data.count === 'number') {
                setPendingCount(data.count);
              }
              if (data.requests) {
                setPendingRequests(data.requests);
              }
              console.log('Notification update received:', data);
              break;
              
            case 'heartbeat':
              // Just log heartbeat, no action needed
              console.log('SSE heartbeat received');
              break;
              
            case 'error':
              console.error('SSE error:', data.message);
              setError(data.message || 'Unknown error');
              break;
              
            default:
              console.log('Unknown SSE message type:', data.type);
          }
        } catch (err) {
          console.error('Error parsing SSE message:', err);
          setError('Error parsing notification data');
        }
      };

      eventSource.onerror = (event) => {
        console.error('SSE error event:', event);
        console.error('EventSource readyState:', eventSource.readyState);
        setIsConnected(false);

        // Only attempt reconnection if the connection was closed unexpectedly
        if (eventSource.readyState === EventSource.CLOSED) {
          setError('Connection closed by server');

          // Attempt to reconnect with exponential backoff
          if (reconnectAttemptsRef.current < maxReconnectAttempts) {
            const delay = Math.min(Math.pow(2, reconnectAttemptsRef.current) * 1000, 30000); // Max 30s delay
            reconnectAttemptsRef.current++;

            console.log(`Attempting to reconnect in ${delay}ms (attempt ${reconnectAttemptsRef.current}/${maxReconnectAttempts})`);

            reconnectTimeoutRef.current = setTimeout(() => {
              connect();
            }, delay);
          } else {
            setError('Failed to connect to real-time notifications after multiple attempts');
          }
        } else if (eventSource.readyState === EventSource.CONNECTING) {
          setError('Connection failed - retrying...');
        } else {
          setError('Connection error occurred');
        }
      };

    } catch (err) {
      console.error('Error creating SSE connection:', err);
      setError('Failed to establish real-time connection');
    }
  }, [user, cleanup]);

  const reconnect = useCallback(() => {
    reconnectAttemptsRef.current = 0;
    setError(null);
    connect();
  }, [connect]);

  // Initialize connection when user changes
  useEffect(() => {
    if (user?.user_type === 'E') {
      connect();
    } else {
      cleanup();
    }

    return cleanup;
  }, [user, connect, cleanup]);

  // Cleanup on unmount
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  // Handle page visibility changes to reconnect when page becomes visible
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && user?.user_type === 'E' && !isConnected) {
        console.log('Page became visible, attempting to reconnect SSE');
        reconnect();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [user, isConnected, reconnect]);

  return {
    pendingCount,
    pendingRequests,
    isConnected,
    error,
    reconnect,
  };
};
