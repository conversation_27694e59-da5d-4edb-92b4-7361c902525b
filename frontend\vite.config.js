import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';
export default defineConfig({
    plugins: [react()],
    resolve: {
        alias: {
            '@': path.resolve(__dirname, './src'),
        },
    },
    server: {
        host: false,
        port: 3000,
        watch: {
            usePolling: true,
            interval: 1000,
        },
        fs: {
            strict: false,
        },
    },
    optimizeDeps: {
        force: true,
        include: ['react', 'react-dom', '@mui/material', '@mui/icons-material'],
    },
    build: {
        outDir: 'dist',
        sourcemap: false,
        minify: true,
        chunkSizeWarningLimit: 1000,
        rollupOptions: {
            output: {
                manualChunks: {
                    vendor: ['react', 'react-dom'],
                    mui: ['@mui/material', '@mui/icons-material'],
                },
            },
        },
    },
    cacheDir: './.vite-cache',
});
